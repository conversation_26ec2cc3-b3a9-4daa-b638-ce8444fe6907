'use client';
import { Box, Container, Typography, Grid, Card, CardContent, Avatar, Rating } from '@mui/material';
import { styled } from '@mui/material/system';
import { FormatQuote, Google } from '@mui/icons-material';

const StyledSection = styled(Box)(({ theme }) => ({
  padding: '80px 0',
  background: 'linear-gradient(135deg, #FFFFFF 0%, #F9ECCE 100%)',
  position: 'relative',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundImage: `
      radial-gradient(circle at 15% 85%, rgba(212, 148, 26, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 85% 15%, rgba(30, 58, 138, 0.1) 0%, transparent 50%)
    `,
    zIndex: 0,
  },
}));

const TestimonialCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(10px)',
  borderRadius: '25px',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(212, 148, 26, 0.1)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  height: '100%',
  position: 'relative',
  zIndex: 1,
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15), 0 4px 16px rgba(212, 148, 26, 0.2)',
  },
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontFamily: "'Playfair Display', serif",
  color: '#2D2D2D',
  textAlign: 'center',
  marginBottom: '20px',
  position: 'relative',
  zIndex: 1,
}));

const SectionSubtitle = styled(Typography)(({ theme }) => ({
  color: '#595958',
  textAlign: 'center',
  marginBottom: '60px',
  position: 'relative',
  zIndex: 1,
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-30px',
    left: '50%',
    transform: 'translateX(-50%)',
    width: '80px',
    height: '3px',
    background: 'linear-gradient(90deg, #D4941A 0%, #1E3A8A 100%)',
    borderRadius: '2px',
  },
}));

const QuoteIcon = styled(FormatQuote)(({ theme }) => ({
  fontSize: '3rem',
  color: '#D4941A',
  opacity: 0.3,
  position: 'absolute',
  top: '20px',
  left: '20px',
}));

const GoogleIcon = styled(Google)(({ theme }) => ({
  color: '#4285F4',
  fontSize: '20px',
  marginRight: '8px',
}));

const testimonials = [
  {
    name: 'Sarah Mueller',
    location: 'Zurich',
    avatar: '/images/avatars/sarah.jpg',
    rating: 5,
    review: 'Absolutely incredible! The Kabuli Pulao was authentic and delicious. The atmosphere perfectly blends Swiss elegance with Afghan warmth. Will definitely be back!',
    date: '2 weeks ago',
    source: 'Google Reviews'
  },
  {
    name: 'Ahmad Hassan',
    location: 'Geneva',
    avatar: '/images/avatars/ahmad.jpg',
    rating: 5,
    review: 'As someone from Afghanistan, I can say this is the most authentic Afghan food I\'ve had in Switzerland. The manto on weekends is exactly like my grandmother used to make.',
    date: '1 month ago',
    source: 'Google Reviews'
  },
  {
    name: 'Maria Rossi',
    location: 'Basel',
    avatar: '/images/avatars/maria.jpg',
    rating: 5,
    review: 'The weekend specials are amazing! We tried the ashak and it was phenomenal. The service is excellent and the cultural ambiance makes you feel like you\'re traveling.',
    date: '3 weeks ago',
    source: 'Google Reviews'
  },
  {
    name: 'Thomas Weber',
    location: 'Bern',
    avatar: '/images/avatars/thomas.jpg',
    rating: 5,
    review: 'Perfect fusion of cultures! The modern presentation with traditional flavors is outstanding. The staff is knowledgeable about the dishes and very welcoming.',
    date: '1 week ago',
    source: 'Google Reviews'
  },
  {
    name: 'Fatima Al-Zahra',
    location: 'Lausanne',
    avatar: '/images/avatars/fatima.jpg',
    rating: 5,
    review: 'The qorma sabzi reminded me of home. Beautiful restaurant with authentic flavors and modern Swiss service standards. Highly recommend the weekend specials!',
    date: '2 months ago',
    source: 'Google Reviews'
  },
  {
    name: 'Jean-Pierre Dubois',
    location: 'Montreux',
    avatar: '/images/avatars/jean.jpg',
    rating: 5,
    review: 'Discovered this gem through a friend. The cultural experience combined with exceptional food makes this a must-visit. The tea service is particularly special.',
    date: '3 weeks ago',
    source: 'Google Reviews'
  }
];

const Testimonials = () => {
  return (
    <StyledSection id="testimonials">
      <Container maxWidth="lg">
        <SectionTitle variant="h2">
          What Our Guests Say
        </SectionTitle>
        <SectionSubtitle variant="h6">
          Authentic experiences shared by our valued customers
        </SectionSubtitle>
        
        <Grid container spacing={4}>
          {testimonials.map((testimonial, index) => (
            <Grid item xs={12} md={6} lg={4} key={index}>
              <TestimonialCard>
                <CardContent sx={{ p: 4, position: 'relative' }}>
                  <QuoteIcon />
                  
                  <Box sx={{ mb: 3, mt: 2 }}>
                    <Rating 
                      value={testimonial.rating} 
                      readOnly 
                      sx={{ 
                        color: '#D4941A',
                        mb: 2
                      }} 
                    />
                    <Typography 
                      variant="body1" 
                      sx={{ 
                        color: '#2D2D2D',
                        lineHeight: 1.7,
                        fontSize: '1rem',
                        fontStyle: 'italic'
                      }}
                    >
                      "{testimonial.review}"
                    </Typography>
                  </Box>
                  
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Avatar 
                        src={testimonial.avatar}
                        alt={testimonial.name}
                        sx={{ 
                          width: 50, 
                          height: 50,
                          border: '2px solid #D4941A'
                        }}
                      />
                      <Box>
                        <Typography 
                          variant="subtitle1" 
                          sx={{ 
                            fontWeight: 600,
                            color: '#2D2D2D',
                            fontSize: '1rem'
                          }}
                        >
                          {testimonial.name}
                        </Typography>
                        <Typography 
                          variant="body2" 
                          sx={{ 
                            color: '#595958',
                            fontSize: '0.85rem'
                          }}
                        >
                          {testimonial.location}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                  
                  <Box 
                    sx={{ 
                      display: 'flex', 
                      alignItems: 'center', 
                      justifyContent: 'space-between',
                      mt: 3,
                      pt: 2,
                      borderTop: '1px solid rgba(212, 148, 26, 0.2)'
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <GoogleIcon />
                      <Typography 
                        variant="body2" 
                        sx={{ 
                          color: '#595958',
                          fontSize: '0.8rem'
                        }}
                      >
                        {testimonial.source}
                      </Typography>
                    </Box>
                    <Typography 
                      variant="body2" 
                      sx={{ 
                        color: '#8C8C8B',
                        fontSize: '0.8rem'
                      }}
                    >
                      {testimonial.date}
                    </Typography>
                  </Box>
                </CardContent>
              </TestimonialCard>
            </Grid>
          ))}
        </Grid>
        
        <Box sx={{ textAlign: 'center', mt: 6, position: 'relative', zIndex: 1 }}>
          <Typography 
            variant="h4" 
            sx={{ 
              fontFamily: "'Playfair Display', serif",
              color: '#D4941A',
              fontWeight: 600,
              mb: 2
            }}
          >
            4.9/5
          </Typography>
          <Typography 
            variant="body1" 
            sx={{ 
              color: '#595958',
              mb: 1
            }}
          >
            Average rating from 127+ Google Reviews
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
            <GoogleIcon />
            <Typography 
              variant="body2" 
              sx={{ 
                color: '#4285F4',
                fontWeight: 600
              }}
            >
              View all reviews on Google
            </Typography>
          </Box>
        </Box>
      </Container>
    </StyledSection>
  );
};

export default Testimonials;
