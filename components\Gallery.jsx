'use client';
import { Box, Container, Typography, Grid, Modal, Icon<PERSON>utton } from '@mui/material';
import { styled } from '@mui/material/system';
import Image from 'next/image';
import { useState } from 'react';
import { Close, ArrowBackIos, ArrowForwardIos } from '@mui/icons-material';

const StyledSection = styled(Box)(({ theme }) => ({
  padding: '80px 0',
  background: 'linear-gradient(135deg, #F5F5F4 0%, #FFFFFF 100%)',
  position: 'relative',
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontFamily: "'Playfair Display', serif",
  color: '#2D2D2D',
  textAlign: 'center',
  marginBottom: '60px',
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-15px',
    left: '50%',
    transform: 'translateX(-50%)',
    width: '80px',
    height: '3px',
    background: 'linear-gradient(90deg, #D4941A 0%, #1E3A8A 100%)',
    borderRadius: '2px',
  },
}));

const GalleryItem = styled(Box)(({ theme }) => ({
  position: 'relative',
  borderRadius: '20px',
  overflow: 'hidden',
  cursor: 'pointer',
  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
  '&:hover': {
    transform: 'translateY(-8px) scale(1.02)',
    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.2), 0 4px 16px rgba(212, 148, 26, 0.2)',
    '& .gallery-overlay': {
      opacity: 1,
    },
    '& img': {
      transform: 'scale(1.1)',
    },
  },
}));

const GalleryOverlay = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  background: 'linear-gradient(135deg, rgba(212, 148, 26, 0.8) 0%, rgba(30, 58, 138, 0.8) 100%)',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  opacity: 0,
  transition: 'opacity 0.3s ease',
  zIndex: 2,
}));

const ModalContent = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  maxWidth: '90vw',
  maxHeight: '90vh',
  outline: 'none',
  borderRadius: '20px',
  overflow: 'hidden',
  boxShadow: '0 25px 50px rgba(0, 0, 0, 0.5)',
}));

const NavigationButton = styled(IconButton)(({ theme }) => ({
  position: 'absolute',
  top: '50%',
  transform: 'translateY(-50%)',
  background: 'rgba(0, 0, 0, 0.5)',
  color: 'white',
  width: '50px',
  height: '50px',
  '&:hover': {
    background: 'rgba(0, 0, 0, 0.7)',
  },
}));

const CloseButton = styled(IconButton)(({ theme }) => ({
  position: 'absolute',
  top: '20px',
  right: '20px',
  background: 'rgba(0, 0, 0, 0.5)',
  color: 'white',
  zIndex: 10,
  '&:hover': {
    background: 'rgba(0, 0, 0, 0.7)',
  },
}));

const galleryImages = [
  {
    src: '/images/gallery/restaurant-interior.jpg',
    alt: 'Modern Swiss-Afghan restaurant interior with warm lighting',
    category: 'ambiance',
    height: 300,
  },
  {
    src: '/images/gallery/kabuli-pulao-close.jpg',
    alt: 'Close-up of traditional Kabuli Pulao with garnishes',
    category: 'food',
    height: 200,
  },
  {
    src: '/images/gallery/happy-customers.jpg',
    alt: 'Happy customers enjoying their meal',
    category: 'customers',
    height: 250,
  },
  {
    src: '/images/gallery/chef-cooking.jpg',
    alt: 'Chef preparing traditional Afghan dishes',
    category: 'kitchen',
    height: 280,
  },
  {
    src: '/images/gallery/manto-preparation.jpg',
    alt: 'Traditional manto dumplings being prepared',
    category: 'food',
    height: 220,
  },
  {
    src: '/images/gallery/dining-area.jpg',
    alt: 'Elegant dining area with Afghan cultural elements',
    category: 'ambiance',
    height: 260,
  },
  {
    src: '/images/gallery/spice-display.jpg',
    alt: 'Beautiful display of traditional Afghan spices',
    category: 'ingredients',
    height: 240,
  },
  {
    src: '/images/gallery/family-dinner.jpg',
    alt: 'Family enjoying traditional Afghan dinner',
    category: 'customers',
    height: 290,
  },
  {
    src: '/images/gallery/tea-service.jpg',
    alt: 'Traditional Afghan tea service',
    category: 'food',
    height: 200,
  },
];

const Gallery = () => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [currentIndex, setCurrentIndex] = useState(0);

  const openModal = (index) => {
    setCurrentIndex(index);
    setSelectedImage(galleryImages[index]);
  };

  const closeModal = () => {
    setSelectedImage(null);
  };

  const navigateImage = (direction) => {
    const newIndex = direction === 'next' 
      ? (currentIndex + 1) % galleryImages.length
      : (currentIndex - 1 + galleryImages.length) % galleryImages.length;
    
    setCurrentIndex(newIndex);
    setSelectedImage(galleryImages[newIndex]);
  };

  return (
    <StyledSection id="gallery">
      <Container maxWidth="lg">
        <SectionTitle variant="h2">
          Gallery
        </SectionTitle>
        
        <Grid container spacing={3}>
          {galleryImages.map((image, index) => (
            <Grid 
              item 
              xs={12} 
              sm={6} 
              md={4} 
              key={index}
              sx={{ 
                display: 'flex',
                alignItems: index % 3 === 1 ? 'flex-end' : 'flex-start'
              }}
            >
              <GalleryItem 
                sx={{ 
                  height: `${image.height}px`,
                  width: '100%'
                }}
                onClick={() => openModal(index)}
              >
                <Image
                  src={image.src}
                  alt={image.alt}
                  fill
                  style={{ 
                    objectFit: 'cover',
                    transition: 'transform 0.4s ease'
                  }}
                  placeholder="blur"
                  blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
                />
                <GalleryOverlay className="gallery-overlay">
                  <Typography 
                    variant="h6" 
                    sx={{ 
                      color: 'white',
                      fontWeight: 600,
                      textAlign: 'center',
                      textShadow: '0 2px 4px rgba(0,0,0,0.5)'
                    }}
                  >
                    View Image
                  </Typography>
                </GalleryOverlay>
              </GalleryItem>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Lightbox Modal */}
      <Modal
        open={!!selectedImage}
        onClose={closeModal}
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          p: 2,
        }}
      >
        <ModalContent>
          {selectedImage && (
            <>
              <CloseButton onClick={closeModal}>
                <Close />
              </CloseButton>
              
              <NavigationButton 
                sx={{ left: '20px' }}
                onClick={() => navigateImage('prev')}
              >
                <ArrowBackIos />
              </NavigationButton>
              
              <NavigationButton 
                sx={{ right: '20px' }}
                onClick={() => navigateImage('next')}
              >
                <ArrowForwardIos />
              </NavigationButton>
              
              <Box sx={{ position: 'relative', width: '80vw', height: '70vh' }}>
                <Image
                  src={selectedImage.src}
                  alt={selectedImage.alt}
                  fill
                  style={{ objectFit: 'contain' }}
                  priority
                />
              </Box>
              
              <Box 
                sx={{
                  position: 'absolute',
                  bottom: 0,
                  left: 0,
                  right: 0,
                  background: 'linear-gradient(transparent, rgba(0,0,0,0.8))',
                  color: 'white',
                  p: 3,
                  textAlign: 'center',
                }}
              >
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                  {selectedImage.alt}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.8 }}>
                  {currentIndex + 1} of {galleryImages.length}
                </Typography>
              </Box>
            </>
          )}
        </ModalContent>
      </Modal>
    </StyledSection>
  );
};

export default Gallery;
