'use client';
import { Box, Container, Typography, Grid, Card, CardContent, CardMedia, Button } from '@mui/material';
import { styled } from '@mui/material/system';
import Image from 'next/image';

const StyledSection = styled(Box)(({ theme }) => ({
  padding: '80px 0',
  background: 'linear-gradient(135deg, #FFFFFF 0%, #F5F5F4 100%)',
  position: 'relative',
}));

const MenuCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(10px)',
  borderRadius: '20px',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(212, 148, 26, 0.1)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  height: '100%',
  cursor: 'pointer',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15), 0 4px 16px rgba(212, 148, 26, 0.2)',
  },
}));

const CategoryTitle = styled(Typography)(({ theme }) => ({
  fontFamily: "'Playfair Display', serif",
  color: '#2D2D2D',
  fontWeight: 600,
  marginBottom: '12px',
}));

const DishName = styled(Typography)(({ theme }) => ({
  fontWeight: 600,
  color: '#2D2D2D',
  marginBottom: '8px',
}));

const Description = styled(Typography)(({ theme }) => ({
  color: '#595958',
  lineHeight: 1.6,
  fontSize: '0.9rem',
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontFamily: "'Playfair Display', serif",
  color: '#2D2D2D',
  textAlign: 'center',
  marginBottom: '60px',
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-15px',
    left: '50%',
    transform: 'translateX(-50%)',
    width: '80px',
    height: '3px',
    background: 'linear-gradient(90deg, #D4941A 0%, #1E3A8A 100%)',
    borderRadius: '2px',
  },
}));

const ModernButton = styled(Button)(({ theme }) => ({
  background: 'linear-gradient(135deg, #D4941A 0%, #B87F16 100%)',
  color: 'white',
  borderRadius: '50px',
  padding: '12px 32px',
  fontWeight: 600,
  fontSize: '16px',
  textTransform: 'none',
  position: 'relative',
  overflow: 'hidden',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: '50%',
    left: '50%',
    width: 0,
    height: 0,
    background: 'rgba(255, 255, 255, 0.2)',
    borderRadius: '50%',
    transform: 'translate(-50%, -50%)',
    transition: 'all 0.3s ease',
  },
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 8px 25px rgba(212, 148, 26, 0.4)',
    '&::before': {
      width: '300px',
      height: '300px',
    },
  },
}));

const menuCategories = [
  {
    title: 'Rice Dishes',
    titlePersian: 'پولو',
    dishes: [
      {
        name: 'Kabuli Pulao',
        description: 'Aromatic basmati rice with tender lamb, carrots, and raisins',
        image: '/images/kabuli-pulao.jpg'
      },
      {
        name: 'Zereshk Polo',
        description: 'Saffron rice with barberries and almonds',
        image: '/images/zereshk-polo.jpg'
      }
    ]
  },
  {
    title: 'Qorma',
    titlePersian: 'قورمه',
    dishes: [
      {
        name: 'Qorma Sabzi',
        description: 'Herb stew with tender beef and kidney beans',
        image: '/images/qorma-sabzi.jpg'
      },
      {
        name: 'Qorma Chalow',
        description: 'Traditional beef curry with aromatic spices',
        image: '/images/qorma-chalow.jpg'
      }
    ]
  },
  {
    title: 'Starters',
    titlePersian: 'مقبلات',
    dishes: [
      {
        name: 'Hummus & Naan',
        description: 'Creamy hummus with fresh Afghan bread',
        image: '/images/hummus-naan.jpg'
      },
      {
        name: 'Kashk Bademjan',
        description: 'Fried eggplant with whey and mint',
        image: '/images/kashk-bademjan.jpg'
      }
    ]
  },
  {
    title: 'Drinks',
    titlePersian: 'نوشیدنی',
    dishes: [
      {
        name: 'Doogh',
        description: 'Traditional yogurt drink with mint',
        image: '/images/doogh.jpg'
      },
      {
        name: 'Chai Sabz',
        description: 'Green tea with cardamom and sugar',
        image: '/images/chai-sabz.jpg'
      }
    ]
  }
];

const MenuHighlights = () => {
  return (
    <StyledSection id="menu-highlights">
      <Container maxWidth="lg">
        <SectionTitle variant="h2">
          Menu Highlights
        </SectionTitle>
        
        <Grid container spacing={4}>
          {menuCategories.map((category, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <MenuCard>
                <Box sx={{ p: 3, textAlign: 'center' }}>
                  <CategoryTitle variant="h5">
                    {category.title}
                  </CategoryTitle>
                  <Typography 
                    variant="h6" 
                    sx={{ 
                      fontFamily: "'Playfair Display', serif",
                      color: '#D4941A',
                      mb: 3,
                      fontSize: '1.2rem'
                    }}
                  >
                    {category.titlePersian}
                  </Typography>
                </Box>
                
                {category.dishes.map((dish, dishIndex) => (
                  <Box key={dishIndex} sx={{ mb: dishIndex === 0 ? 2 : 0 }}>
                    <Box 
                      sx={{ 
                        position: 'relative',
                        height: '120px',
                        overflow: 'hidden',
                        borderRadius: dishIndex === 0 ? '0' : '0 0 20px 20px'
                      }}
                    >
                      <Image
                        src={dish.image}
                        alt={dish.name}
                        fill
                        style={{ objectFit: 'cover' }}
                        placeholder="blur"
                        blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
                      />
                      <Box 
                        sx={{
                          position: 'absolute',
                          bottom: 0,
                          left: 0,
                          right: 0,
                          background: 'linear-gradient(transparent, rgba(0,0,0,0.7))',
                          color: 'white',
                          p: 2,
                        }}
                      >
                        <DishName variant="subtitle1" sx={{ color: 'white', fontSize: '0.9rem' }}>
                          {dish.name}
                        </DishName>
                        <Description variant="body2" sx={{ color: 'rgba(255,255,255,0.9)', fontSize: '0.8rem' }}>
                          {dish.description}
                        </Description>
                      </Box>
                    </Box>
                  </Box>
                ))}
              </MenuCard>
            </Grid>
          ))}
        </Grid>
        
        <Box sx={{ textAlign: 'center', mt: 6 }}>
          <ModernButton size="large">
            View Full Menu
          </ModernButton>
        </Box>
      </Container>
    </StyledSection>
  );
};

export default MenuHighlights;
