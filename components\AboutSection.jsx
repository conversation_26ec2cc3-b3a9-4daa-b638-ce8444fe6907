'use client';
import { Box, Container, Typography, Grid, Card, CardContent } from '@mui/material';
import { styled } from '@mui/material/system';
import Image from 'next/image';

const StyledSection = styled(Box)(({ theme }) => ({
  padding: '80px 0',
  position: 'relative',
  background: 'linear-gradient(135deg, #F9ECCE 0%, rgba(249, 236, 206, 0.3) 100%)',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundImage: `
      radial-gradient(circle at 25% 25%, #D4941A 2px, transparent 2px),
      radial-gradient(circle at 75% 75%, #1E3A8A 1px, transparent 1px)
    `,
    backgroundSize: '40px 40px, 20px 20px',
    opacity: 0.1,
    zIndex: 0,
  },
}));

const StyledCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(10px)',
  borderRadius: '20px',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(212, 148, 26, 0.1)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  position: 'relative',
  zIndex: 1,
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15), 0 4px 16px rgba(212, 148, 26, 0.2)',
  },
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontFamily: "'Playfair Display', serif",
  color: '#2D2D2D',
  textAlign: 'center',
  marginBottom: '60px',
  position: 'relative',
  zIndex: 1,
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-15px',
    left: '50%',
    transform: 'translateX(-50%)',
    width: '80px',
    height: '3px',
    background: 'linear-gradient(90deg, #D4941A 0%, #1E3A8A 100%)',
    borderRadius: '2px',
  },
}));

const StyledImageContainer = styled(Box)(({ theme }) => ({
  position: 'relative',
  borderRadius: '15px',
  overflow: 'hidden',
  height: '300px',
  '& img': {
    transition: 'transform 0.3s ease',
  },
  '&:hover img': {
    transform: 'scale(1.05)',
  },
}));

const AboutSection = () => {
  return (
    <StyledSection id="about">
      <Container maxWidth="lg">
        <SectionTitle variant="h2">
          Our Story
        </SectionTitle>
        
        <Grid container spacing={4} alignItems="center">
          <Grid item xs={12} md={6}>
            <StyledCard>
              <CardContent sx={{ p: 4 }}>
                <Typography 
                  variant="h4" 
                  sx={{ 
                    fontFamily: "'Playfair Display', serif",
                    color: '#2D2D2D',
                    mb: 3,
                    fontWeight: 600
                  }}
                >
                  Welcome to Polo
                </Typography>
                
                <Typography 
                  variant="body1" 
                  sx={{ 
                    color: '#595958',
                    lineHeight: 1.8,
                    mb: 3,
                    fontSize: '1.1rem'
                  }}
                >
                  Named after the ancient game that bridges cultures, Polo represents the beautiful 
                  fusion of Swiss precision and Afghan warmth. Our restaurant brings together the 
                  finest traditions from both worlds.
                </Typography>
                
                <Typography 
                  variant="body1" 
                  sx={{ 
                    color: '#595958',
                    lineHeight: 1.8,
                    mb: 3,
                    fontSize: '1.1rem'
                  }}
                >
                  From the snow-capped Alps to the historic valleys of Afghanistan, we celebrate 
                  the rich culinary heritage that connects these distant lands through shared 
                  values of hospitality, quality, and tradition.
                </Typography>
                
                <Box sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  gap: 2,
                  mt: 4,
                  p: 3,
                  background: 'linear-gradient(135deg, rgba(212, 148, 26, 0.1) 0%, rgba(30, 58, 138, 0.1) 100%)',
                  borderRadius: '12px',
                  border: '1px solid rgba(212, 148, 26, 0.2)'
                }}>
                  <Typography 
                    variant="h6" 
                    sx={{ 
                      fontFamily: "'Playfair Display', serif",
                      color: '#D4941A',
                      fontWeight: 600
                    }}
                  >
                    پولو
                  </Typography>
                  <Typography 
                    variant="body2" 
                    sx={{ 
                      color: '#595958',
                      fontStyle: 'italic'
                    }}
                  >
                    "Polo" in Dari - meaning rice, the heart of Afghan cuisine
                  </Typography>
                </Box>
              </CardContent>
            </StyledCard>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <StyledImageContainer>
                  <Image
                    src="/images/kitchen-rice-dishes.jpg"
                    alt="Traditional Afghan rice dishes in our kitchen"
                    fill
                    style={{ objectFit: 'cover' }}
                    placeholder="blur"
                    blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
                  />
                </StyledImageContainer>
              </Grid>
              
              <Grid item xs={6}>
                <StyledImageContainer sx={{ height: '140px' }}>
                  <Image
                    src="/images/afghan-spices.jpg"
                    alt="Traditional Afghan spices and ingredients"
                    fill
                    style={{ objectFit: 'cover' }}
                    placeholder="blur"
                    blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
                  />
                </StyledImageContainer>
              </Grid>
              
              <Grid item xs={6}>
                <StyledImageContainer sx={{ height: '140px' }}>
                  <Image
                    src="/images/swiss-alpine.jpg"
                    alt="Swiss Alpine inspiration"
                    fill
                    style={{ objectFit: 'cover' }}
                    placeholder="blur"
                    blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
                  />
                </StyledImageContainer>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Container>
    </StyledSection>
  );
};

export default AboutSection;
