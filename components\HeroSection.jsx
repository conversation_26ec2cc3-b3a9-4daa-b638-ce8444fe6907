'use client';
import { Box, Container, Typography, Button, Grid } from '@mui/material';
import { styled } from '@mui/material/system';
import Image from 'next/image';
import { Restaurant, Schedule } from '@mui/icons-material';

const HeroContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  background: 'linear-gradient(135deg, #F3FFCF 0%, rgba(243, 255, 207, 0.8) 100%)',
  position: 'relative',
  display: 'flex',
  alignItems: 'center',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundImage: `
      radial-gradient(circle at 20% 80%, rgba(212, 148, 26, 0.2) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(30, 58, 138, 0.2) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(212, 148, 26, 0.1) 0%, transparent 30%)
    `,
    backgroundSize: '60px 60px, 40px 40px, 80px 80px',
    animation: 'afghanPattern 25s ease-in-out infinite',
    zIndex: 0,
  },
}));

const ContentWrapper = styled(Box)(({ theme }) => ({
  position: 'relative',
  zIndex: 2,
  width: '100%',
}));

const HeroTitle = styled(Typography)(({ theme }) => ({
  fontFamily: "'Playfair Display', serif",
  fontWeight: 700,
  color: '#2D2D2D',
  marginBottom: '20px',
  textShadow: '0 2px 4px rgba(0,0,0,0.1)',
  lineHeight: 1.2,
}));

const PersianTitle = styled(Typography)(({ theme }) => ({
  fontFamily: "'Playfair Display', serif",
  color: '#D4941A',
  fontWeight: 600,
  marginBottom: '30px',
  textShadow: '0 2px 4px rgba(0,0,0,0.1)',
}));

const HeroSubtitle = styled(Typography)(({ theme }) => ({
  color: '#595958',
  marginBottom: '40px',
  lineHeight: 1.7,
  fontSize: '1.2rem',
  maxWidth: '600px',
}));

const ModernButton = styled(Button)(({ theme }) => ({
  background: 'linear-gradient(135deg, #D4941A 0%, #B87F16 100%)',
  color: 'white',
  borderRadius: '50px',
  padding: '16px 40px',
  fontWeight: 600,
  fontSize: '18px',
  textTransform: 'none',
  marginRight: '20px',
  marginBottom: '20px',
  position: 'relative',
  overflow: 'hidden',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: '50%',
    left: '50%',
    width: 0,
    height: 0,
    background: 'rgba(255, 255, 255, 0.2)',
    borderRadius: '50%',
    transform: 'translate(-50%, -50%)',
    transition: 'all 0.3s ease',
  },
  '&:hover': {
    transform: 'translateY(-3px)',
    boxShadow: '0 12px 30px rgba(212, 148, 26, 0.4)',
    '&::before': {
      width: '300px',
      height: '300px',
    },
  },
}));

const SecondaryButton = styled(Button)(({ theme }) => ({
  borderColor: '#2D2D2D',
  color: '#2D2D2D',
  borderRadius: '50px',
  padding: '16px 40px',
  fontWeight: 600,
  fontSize: '18px',
  textTransform: 'none',
  marginBottom: '20px',
  transition: 'all 0.3s ease',
  '&:hover': {
    borderColor: '#D4941A',
    color: '#D4941A',
    background: 'rgba(212, 148, 26, 0.1)',
    transform: 'translateY(-3px)',
  },
}));

const FeatureCard = styled(Box)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.9)',
  backdropFilter: 'blur(10px)',
  borderRadius: '20px',
  padding: '30px',
  textAlign: 'center',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  transition: 'all 0.3s ease',
  height: '100%',
  '&:hover': {
    transform: 'translateY(-5px)',
    boxShadow: '0 15px 40px rgba(0, 0, 0, 0.15)',
  },
}));

const IconWrapper = styled(Box)(({ theme }) => ({
  width: '70px',
  height: '70px',
  borderRadius: '50%',
  background: 'linear-gradient(135deg, #D4941A 0%, #B87F16 100%)',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  margin: '0 auto 20px',
  color: 'white',
}));

const HeroImageContainer = styled(Box)(({ theme }) => ({
  position: 'relative',
  height: '500px',
  borderRadius: '25px',
  overflow: 'hidden',
  boxShadow: '0 20px 60px rgba(0, 0, 0, 0.2)',
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'linear-gradient(135deg, rgba(212, 148, 26, 0.1) 0%, rgba(30, 58, 138, 0.1) 100%)',
    zIndex: 1,
  },
}));

const HeroSection = () => {
  return (
    <HeroContainer>
      <Container maxWidth="lg">
        <ContentWrapper>
          <Grid container spacing={6} alignItems="center">
            <Grid item xs={12} lg={6}>
              <HeroTitle variant="h1">
                Welcome to Polo
              </HeroTitle>
              <PersianTitle variant="h3">
                پولو - Where Cultures Meet
              </PersianTitle>
              <HeroSubtitle variant="h6">
                Experience the authentic flavors of Afghanistan with Swiss precision and hospitality. 
                Our restaurant brings together two rich culinary traditions in the heart of Zurich.
              </HeroSubtitle>
              
              <Box sx={{ mb: 5 }}>
                <ModernButton 
                  size="large"
                  startIcon={<Restaurant />}
                >
                  View Menu
                </ModernButton>
                <SecondaryButton 
                  variant="outlined" 
                  size="large"
                  startIcon={<Schedule />}
                >
                  Make Reservation
                </SecondaryButton>
              </Box>
              
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <FeatureCard>
                    <IconWrapper>
                      <Restaurant sx={{ fontSize: '30px' }} />
                    </IconWrapper>
                    <Typography 
                      variant="h6" 
                      sx={{ 
                        fontFamily: "'Playfair Display', serif",
                        color: '#2D2D2D',
                        mb: 1,
                        fontWeight: 600
                      }}
                    >
                      Authentic Cuisine
                    </Typography>
                    <Typography 
                      variant="body2" 
                      sx={{ 
                        color: '#595958',
                        lineHeight: 1.6
                      }}
                    >
                      Traditional Afghan recipes passed down through generations
                    </Typography>
                  </FeatureCard>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FeatureCard>
                    <IconWrapper>
                      <Schedule sx={{ fontSize: '30px' }} />
                    </IconWrapper>
                    <Typography 
                      variant="h6" 
                      sx={{ 
                        fontFamily: "'Playfair Display', serif",
                        color: '#2D2D2D',
                        mb: 1,
                        fontWeight: 600
                      }}
                    >
                      Weekend Specials
                    </Typography>
                    <Typography 
                      variant="body2" 
                      sx={{ 
                        color: '#595958',
                        lineHeight: 1.6
                      }}
                    >
                      منتو، آشک، بولانی available Friday through Sunday
                    </Typography>
                  </FeatureCard>
                </Grid>
              </Grid>
            </Grid>
            
            <Grid item xs={12} lg={6}>
              <HeroImageContainer>
                <Image
                  src="/images/hero-restaurant.jpg"
                  alt="Polo Restaurant - Swiss-Afghan cuisine in Zurich"
                  fill
                  style={{ objectFit: 'cover' }}
                  priority
                  placeholder="blur"
                  blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
                />
              </HeroImageContainer>
            </Grid>
          </Grid>
        </ContentWrapper>
      </Container>
    </HeroContainer>
  );
};

export default HeroSection;
