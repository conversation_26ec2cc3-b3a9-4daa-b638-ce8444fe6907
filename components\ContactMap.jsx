'use client';
import { 
  Box, 
  Container, 
  Typo<PERSON>, 
  Grid, 
  Card, 
  CardContent, 
  TextField, 
  Button,
  IconButton,
  Divider
} from '@mui/material';
import { styled } from '@mui/material/system';
import { 
  Phone, 
  Email, 
  LocationOn, 
  WhatsApp, 
  AccessTime,
  Send
} from '@mui/icons-material';

const StyledSection = styled(Box)(({ theme }) => ({
  padding: '80px 0',
  background: 'linear-gradient(135deg, #2D2D2D 0%, #1A1A1A 100%)',
  color: 'white',
  position: 'relative',
}));

const ContactCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(15px)',
  borderRadius: '25px',
  boxShadow: '0 12px 40px rgba(0, 0, 0, 0.3), 0 4px 16px rgba(212, 148, 26, 0.2)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  height: '100%',
}));

const MapContainer = styled(Box)(({ theme }) => ({
  borderRadius: '25px',
  overflow: 'hidden',
  height: '400px',
  boxShadow: '0 12px 40px rgba(0, 0, 0, 0.3)',
  border: '1px solid rgba(255, 255, 255, 0.1)',
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontFamily: "'Playfair Display', serif",
  color: 'white',
  textAlign: 'center',
  marginBottom: '60px',
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-15px',
    left: '50%',
    transform: 'translateX(-50%)',
    width: '80px',
    height: '3px',
    background: 'linear-gradient(90deg, #D4941A 0%, #1E3A8A 100%)',
    borderRadius: '2px',
  },
}));

const ContactItem = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: '16px',
  marginBottom: '24px',
  padding: '16px',
  borderRadius: '15px',
  background: 'linear-gradient(135deg, rgba(212, 148, 26, 0.1) 0%, rgba(30, 58, 138, 0.1) 100%)',
  border: '1px solid rgba(212, 148, 26, 0.2)',
  transition: 'all 0.3s ease',
  '&:hover': {
    background: 'linear-gradient(135deg, rgba(212, 148, 26, 0.15) 0%, rgba(30, 58, 138, 0.15) 100%)',
    transform: 'translateY(-2px)',
  },
}));

const IconWrapper = styled(Box)(({ theme }) => ({
  width: '50px',
  height: '50px',
  borderRadius: '50%',
  background: 'linear-gradient(135deg, #D4941A 0%, #B87F16 100%)',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  color: 'white',
  flexShrink: 0,
}));

const ModernButton = styled(Button)(({ theme }) => ({
  background: 'linear-gradient(135deg, #D4941A 0%, #B87F16 100%)',
  color: 'white',
  borderRadius: '50px',
  padding: '12px 32px',
  fontWeight: 600,
  fontSize: '16px',
  textTransform: 'none',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 8px 25px rgba(212, 148, 26, 0.4)',
  },
}));

const WhatsAppButton = styled(IconButton)(({ theme }) => ({
  background: 'linear-gradient(135deg, #25D366 0%, #128C7E 100%)',
  color: 'white',
  width: '60px',
  height: '60px',
  '&:hover': {
    background: 'linear-gradient(135deg, #128C7E 0%, #075E54 100%)',
    transform: 'scale(1.1)',
  },
}));

const ContactMap = () => {
  return (
    <StyledSection id="contact">
      <Container maxWidth="lg">
        <SectionTitle variant="h2">
          Visit Us
        </SectionTitle>
        
        <Grid container spacing={4}>
          {/* Contact Information */}
          <Grid item xs={12} md={6}>
            <ContactCard>
              <CardContent sx={{ p: 4 }}>
                <Typography 
                  variant="h4" 
                  sx={{ 
                    fontFamily: "'Playfair Display', serif",
                    color: '#2D2D2D',
                    mb: 3,
                    fontWeight: 600
                  }}
                >
                  Get in Touch
                </Typography>
                
                <ContactItem>
                  <IconWrapper>
                    <LocationOn />
                  </IconWrapper>
                  <Box>
                    <Typography variant="h6" sx={{ color: '#2D2D2D', fontWeight: 600 }}>
                      Address
                    </Typography>
                    <Typography variant="body1" sx={{ color: '#595958' }}>
                      Bahnhofstrasse 123<br />
                      8001 Zurich, Switzerland
                    </Typography>
                  </Box>
                </ContactItem>
                
                <ContactItem>
                  <IconWrapper>
                    <Phone />
                  </IconWrapper>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="h6" sx={{ color: '#2D2D2D', fontWeight: 600 }}>
                      Phone
                    </Typography>
                    <Typography variant="body1" sx={{ color: '#595958' }}>
                      +41 44 123 4567
                    </Typography>
                  </Box>
                  <WhatsAppButton size="small">
                    <WhatsApp />
                  </WhatsAppButton>
                </ContactItem>
                
                <ContactItem>
                  <IconWrapper>
                    <Email />
                  </IconWrapper>
                  <Box>
                    <Typography variant="h6" sx={{ color: '#2D2D2D', fontWeight: 600 }}>
                      Email
                    </Typography>
                    <Typography variant="body1" sx={{ color: '#595958' }}>
                      <EMAIL>
                    </Typography>
                  </Box>
                </ContactItem>
                
                <ContactItem>
                  <IconWrapper>
                    <AccessTime />
                  </IconWrapper>
                  <Box>
                    <Typography variant="h6" sx={{ color: '#2D2D2D', fontWeight: 600 }}>
                      Opening Hours
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#595958', lineHeight: 1.6 }}>
                      <strong>Mon-Thu:</strong> 11:30 AM - 10:00 PM<br />
                      <strong>Fri-Sun:</strong> 11:30 AM - 11:00 PM<br />
                      <em>Weekend Specials available Fri-Sun</em>
                    </Typography>
                  </Box>
                </ContactItem>
                
                <Divider sx={{ my: 3, borderColor: 'rgba(212, 148, 26, 0.2)' }} />
                
                <Typography 
                  variant="h5" 
                  sx={{ 
                    fontFamily: "'Playfair Display', serif",
                    color: '#2D2D2D',
                    mb: 3,
                    fontWeight: 600
                  }}
                >
                  Quick Message
                </Typography>
                
                <Box component="form" sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <TextField
                    fullWidth
                    label="Your Name"
                    variant="outlined"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '15px',
                      },
                    }}
                  />
                  <TextField
                    fullWidth
                    label="Email"
                    type="email"
                    variant="outlined"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '15px',
                      },
                    }}
                  />
                  <TextField
                    fullWidth
                    label="Message"
                    multiline
                    rows={3}
                    variant="outlined"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '15px',
                      },
                    }}
                  />
                  <ModernButton 
                    endIcon={<Send />}
                    sx={{ alignSelf: 'flex-start', mt: 1 }}
                  >
                    Send Message
                  </ModernButton>
                </Box>
              </CardContent>
            </ContactCard>
          </Grid>
          
          {/* Map */}
          <Grid item xs={12} md={6}>
            <MapContainer>
              <iframe
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2701.0!2d8.5391825!3d47.3768866!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x47900a0b0b0b0b0b%3A0x0!2sZurich%2C%20Switzerland!5e0!3m2!1sen!2sch!4v1234567890"
                width="100%"
                height="100%"
                style={{ border: 0 }}
                allowFullScreen=""
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                title="Polo Restaurant Location"
              />
            </MapContainer>
            
            <Box sx={{ mt: 3, textAlign: 'center' }}>
              <Typography 
                variant="body1" 
                sx={{ 
                  color: 'rgba(255, 255, 255, 0.9)',
                  mb: 2
                }}
              >
                Located in the heart of Zurich, easily accessible by public transport
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
                <ModernButton variant="outlined" sx={{ 
                  borderColor: 'rgba(255, 255, 255, 0.5)',
                  color: 'white',
                  '&:hover': {
                    borderColor: 'white',
                    background: 'rgba(255, 255, 255, 0.1)',
                  }
                }}>
                  Get Directions
                </ModernButton>
                <ModernButton>
                  Make Reservation
                </ModernButton>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Container>
    </StyledSection>
  );
};

export default ContactMap;
