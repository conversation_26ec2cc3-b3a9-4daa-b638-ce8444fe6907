'use client';
import { Box, Container, Typography, Grid, Card, Card<PERSON>ontent, <PERSON><PERSON>, Chip } from '@mui/material';
import { styled } from '@mui/material/system';
import Image from 'next/image';
import { AccessTime, Restaurant } from '@mui/icons-material';

const StyledSection = styled(Box)(({ theme }) => ({
  padding: '80px 0',
  background: 'linear-gradient(135deg, #1E3A8A 0%, rgba(30, 58, 138, 0.8) 100%)',
  color: 'white',
  position: 'relative',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundImage: `
      radial-gradient(circle at 20% 80%, rgba(212, 148, 26, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%)
    `,
    zIndex: 0,
  },
}));

const SpecialCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(15px)',
  borderRadius: '25px',
  boxShadow: '0 12px 40px rgba(0, 0, 0, 0.2), 0 4px 16px rgba(212, 148, 26, 0.15)',
  border: '1px solid rgba(255, 255, 255, 0.3)',
  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
  height: '100%',
  position: 'relative',
  zIndex: 1,
  overflow: 'hidden',
  '&:hover': {
    transform: 'translateY(-12px) scale(1.02)',
    boxShadow: '0 25px 50px rgba(0, 0, 0, 0.25), 0 8px 32px rgba(212, 148, 26, 0.3)',
  },
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontFamily: "'Playfair Display', serif",
  color: 'white',
  textAlign: 'center',
  marginBottom: '20px',
  position: 'relative',
  zIndex: 1,
}));

const SectionSubtitle = styled(Typography)(({ theme }) => ({
  color: 'rgba(255, 255, 255, 0.9)',
  textAlign: 'center',
  marginBottom: '60px',
  position: 'relative',
  zIndex: 1,
}));

const DishTitle = styled(Typography)(({ theme }) => ({
  fontFamily: "'Playfair Display', serif",
  color: '#2D2D2D',
  fontWeight: 600,
  marginBottom: '12px',
}));

const PersianTitle = styled(Typography)(({ theme }) => ({
  fontFamily: "'Playfair Display', serif",
  color: '#D4941A',
  fontWeight: 600,
  marginBottom: '16px',
  fontSize: '1.3rem',
}));

const ModernButton = styled(Button)(({ theme }) => ({
  background: 'linear-gradient(135deg, #D4941A 0%, #B87F16 100%)',
  color: 'white',
  borderRadius: '50px',
  padding: '10px 24px',
  fontWeight: 600,
  fontSize: '14px',
  textTransform: 'none',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 8px 25px rgba(212, 148, 26, 0.4)',
  },
}));

const AvailabilityChip = styled(Chip)(({ theme }) => ({
  background: 'linear-gradient(135deg, #DC143C 0%, #B91C3C 100%)',
  color: 'white',
  fontWeight: 600,
  fontSize: '12px',
  borderRadius: '20px',
  '& .MuiChip-icon': {
    color: 'white',
  },
}));

const weekendSpecials = [
  {
    name: 'Manto',
    persian: 'منتو',
    description: 'Steamed dumplings filled with spiced ground beef, served with yogurt sauce and lentil curry',
    longDescription: 'Traditional Afghan dumplings, carefully hand-folded and steamed to perfection. Each dumpling contains seasoned ground beef with onions and spices, topped with our signature yogurt sauce and dal (lentil curry).',
    image: '/images/manto.jpg',
    price: 'CHF 24',
    prepTime: '45 min'
  },
  {
    name: 'Ashak',
    persian: 'آشک',
    description: 'Leek-filled dumplings with meat sauce and mint yogurt',
    longDescription: 'Delicate pasta parcels filled with fresh leeks and herbs, topped with a rich meat sauce and cooling mint yogurt. A beloved Afghan comfort food that represents the essence of home cooking.',
    image: '/images/ashak.jpg',
    price: 'CHF 22',
    prepTime: '40 min'
  },
  {
    name: 'Bolani',
    persian: 'بولانی',
    description: 'Crispy flatbread stuffed with potatoes, leeks, or pumpkin',
    longDescription: 'Golden, crispy flatbread stuffed with your choice of seasoned potatoes, fresh leeks, or sweet pumpkin. Served with our house-made chutney and yogurt dipping sauces.',
    image: '/images/bolani.jpg',
    price: 'CHF 18',
    prepTime: '25 min'
  }
];

const WeekendSpecials = () => {
  return (
    <StyledSection id="weekend-specials">
      <Container maxWidth="lg">
        <SectionTitle variant="h2">
          Weekend Specials
        </SectionTitle>
        <SectionSubtitle variant="h6">
          Authentic Afghan delicacies available Friday through Sunday
        </SectionSubtitle>
        
        <Box sx={{ textAlign: 'center', mb: 4, position: 'relative', zIndex: 1 }}>
          <AvailabilityChip 
            icon={<AccessTime />}
            label="Only Available Friday–Sunday"
            size="medium"
          />
        </Box>
        
        <Grid container spacing={4}>
          {weekendSpecials.map((dish, index) => (
            <Grid item xs={12} md={4} key={index}>
              <SpecialCard>
                <Box 
                  sx={{ 
                    position: 'relative',
                    height: '200px',
                    overflow: 'hidden',
                  }}
                >
                  <Image
                    src={dish.image}
                    alt={dish.name}
                    fill
                    style={{ objectFit: 'cover' }}
                    placeholder="blur"
                    blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
                  />
                  <Box 
                    sx={{
                      position: 'absolute',
                      top: 16,
                      right: 16,
                      background: 'rgba(0, 0, 0, 0.7)',
                      color: 'white',
                      padding: '4px 12px',
                      borderRadius: '20px',
                      fontSize: '14px',
                      fontWeight: 600,
                    }}
                  >
                    {dish.price}
                  </Box>
                </Box>
                
                <CardContent sx={{ p: 3 }}>
                  <DishTitle variant="h5">
                    {dish.name}
                  </DishTitle>
                  <PersianTitle variant="h6">
                    {dish.persian}
                  </PersianTitle>
                  
                  <Typography 
                    variant="body2" 
                    sx={{ 
                      color: '#595958',
                      lineHeight: 1.6,
                      mb: 2,
                      fontSize: '0.95rem'
                    }}
                  >
                    {dish.description}
                  </Typography>
                  
                  <Typography 
                    variant="body2" 
                    sx={{ 
                      color: '#8C8C8B',
                      lineHeight: 1.6,
                      mb: 3,
                      fontSize: '0.85rem',
                      fontStyle: 'italic'
                    }}
                  >
                    {dish.longDescription}
                  </Typography>
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Restaurant sx={{ color: '#D4941A', fontSize: '18px' }} />
                      <Typography variant="body2" sx={{ color: '#595958', fontSize: '0.85rem' }}>
                        {dish.prepTime}
                      </Typography>
                    </Box>
                    <ModernButton size="small">
                      Pre-order
                    </ModernButton>
                  </Box>
                </CardContent>
              </SpecialCard>
            </Grid>
          ))}
        </Grid>
        
        <Box sx={{ textAlign: 'center', mt: 6, position: 'relative', zIndex: 1 }}>
          <Typography 
            variant="body1" 
            sx={{ 
              color: 'rgba(255, 255, 255, 0.9)',
              mb: 3,
              fontSize: '1.1rem'
            }}
          >
            Reserve your table for the weekend to enjoy these authentic specialties
          </Typography>
          <ModernButton size="large" sx={{ mr: 2 }}>
            Reserve Now
          </ModernButton>
          <Button 
            variant="outlined" 
            size="large"
            sx={{ 
              borderColor: 'rgba(255, 255, 255, 0.5)',
              color: 'white',
              borderRadius: '50px',
              padding: '12px 32px',
              fontWeight: 600,
              '&:hover': {
                borderColor: 'white',
                background: 'rgba(255, 255, 255, 0.1)',
              }
            }}
          >
            Call Restaurant
          </Button>
        </Box>
      </Container>
    </StyledSection>
  );
};

export default WeekendSpecials;
