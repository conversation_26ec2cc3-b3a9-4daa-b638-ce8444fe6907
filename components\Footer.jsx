'use client';
import { 
  Box, 
  Container, 
  Typography, 
  Grid, 
  IconButton,
  Divider,
  Link
} from '@mui/material';
import { styled } from '@mui/material/system';
import { 
  Instagram, 
  Facebook, 
  Phone, 
  Email, 
  LocationOn,
  AccessTime
} from '@mui/icons-material';

const StyledFooter = styled(Box)(({ theme }) => ({
  background: 'linear-gradient(135deg, #1A1A1A 0%, #000000 100%)',
  color: 'white',
  position: 'relative',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundImage: `
      radial-gradient(circle at 25% 25%, rgba(212, 148, 26, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(30, 58, 138, 0.1) 0%, transparent 50%)
    `,
    zIndex: 0,
  },
}));

const FooterContent = styled(Box)(({ theme }) => ({
  position: 'relative',
  zIndex: 1,
  paddingTop: '60px',
  paddingBottom: '40px',
}));

const LogoSection = styled(Box)(({ theme }) => ({
  textAlign: 'center',
  marginBottom: '40px',
}));

const LogoText = styled(Typography)(({ theme }) => ({
  fontFamily: "'Playfair Display', serif",
  fontSize: '2.5rem',
  fontWeight: 700,
  background: 'linear-gradient(135deg, #D4941A 0%, #1E3A8A 100%)',
  backgroundClip: 'text',
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  marginBottom: '8px',
}));

const PersianText = styled(Typography)(({ theme }) => ({
  fontFamily: "'Playfair Display', serif",
  color: '#D4941A',
  fontSize: '1.2rem',
  fontWeight: 600,
}));

const SocialButton = styled(IconButton)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.1)',
  color: 'white',
  width: '50px',
  height: '50px',
  margin: '0 8px',
  transition: 'all 0.3s ease',
  '&:hover': {
    background: 'linear-gradient(135deg, #D4941A 0%, #B87F16 100%)',
    transform: 'translateY(-3px)',
  },
  '&.instagram:hover': {
    background: 'linear-gradient(135deg, #E4405F 0%, #C13584 100%)',
  },
  '&.facebook:hover': {
    background: 'linear-gradient(135deg, #1877F2 0%, #42A5F5 100%)',
  },
}));

const FooterSection = styled(Box)(({ theme }) => ({
  marginBottom: '30px',
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontFamily: "'Playfair Display', serif",
  color: '#D4941A',
  fontWeight: 600,
  marginBottom: '16px',
  fontSize: '1.2rem',
}));

const FooterLink = styled(Link)(({ theme }) => ({
  color: 'rgba(255, 255, 255, 0.8)',
  textDecoration: 'none',
  display: 'block',
  marginBottom: '8px',
  transition: 'color 0.3s ease',
  '&:hover': {
    color: '#D4941A',
  },
}));

const ContactInfo = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: '12px',
  marginBottom: '12px',
  color: 'rgba(255, 255, 255, 0.8)',
}));

const ContactIcon = styled(Box)(({ theme }) => ({
  width: '24px',
  height: '24px',
  color: '#D4941A',
  flexShrink: 0,
}));

const CopyrightSection = styled(Box)(({ theme }) => ({
  borderTop: '1px solid rgba(255, 255, 255, 0.1)',
  paddingTop: '30px',
  textAlign: 'center',
}));

const Footer = () => {
  return (
    <StyledFooter component="footer">
      <Container maxWidth="lg">
        <FooterContent>
          {/* Logo Section */}
          <LogoSection>
            <LogoText variant="h3">
              Polo
            </LogoText>
            <PersianText variant="h6">
              پولو
            </PersianText>
            <Typography 
              variant="body1" 
              sx={{ 
                color: 'rgba(255, 255, 255, 0.8)',
                mt: 2,
                maxWidth: '500px',
                margin: '16px auto 0',
                lineHeight: 1.6
              }}
            >
              Where Swiss precision meets Afghan warmth. Experience the authentic flavors 
              of Afghanistan in the heart of Switzerland.
            </Typography>
          </LogoSection>

          {/* Social Media */}
          <Box sx={{ textAlign: 'center', mb: 5 }}>
            <SocialButton className="instagram">
              <Instagram />
            </SocialButton>
            <SocialButton className="facebook">
              <Facebook />
            </SocialButton>
          </Box>

          <Grid container spacing={4}>
            {/* Contact Information */}
            <Grid item xs={12} md={4}>
              <FooterSection>
                <SectionTitle variant="h6">
                  Contact Info
                </SectionTitle>
                
                <ContactInfo>
                  <ContactIcon>
                    <LocationOn />
                  </ContactIcon>
                  <Typography variant="body2">
                    Bahnhofstrasse 123<br />
                    8001 Zurich, Switzerland
                  </Typography>
                </ContactInfo>
                
                <ContactInfo>
                  <ContactIcon>
                    <Phone />
                  </ContactIcon>
                  <Typography variant="body2">
                    +41 44 123 4567
                  </Typography>
                </ContactInfo>
                
                <ContactInfo>
                  <ContactIcon>
                    <Email />
                  </ContactIcon>
                  <Typography variant="body2">
                    <EMAIL>
                  </Typography>
                </ContactInfo>
              </FooterSection>
            </Grid>

            {/* Opening Hours */}
            <Grid item xs={12} md={4}>
              <FooterSection>
                <SectionTitle variant="h6">
                  Opening Hours
                </SectionTitle>
                
                <ContactInfo>
                  <ContactIcon>
                    <AccessTime />
                  </ContactIcon>
                  <Box>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>Monday - Thursday</strong><br />
                      11:30 AM - 10:00 PM
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>Friday - Sunday</strong><br />
                      11:30 AM - 11:00 PM
                    </Typography>
                    <Typography 
                      variant="body2" 
                      sx={{ 
                        color: '#D4941A',
                        fontStyle: 'italic',
                        fontSize: '0.85rem'
                      }}
                    >
                      Weekend Specials available Fri-Sun
                    </Typography>
                  </Box>
                </ContactInfo>
              </FooterSection>
            </Grid>

            {/* Quick Links */}
            <Grid item xs={12} md={4}>
              <FooterSection>
                <SectionTitle variant="h6">
                  Quick Links
                </SectionTitle>
                
                <FooterLink href="#about">
                  About Us
                </FooterLink>
                <FooterLink href="#menu-highlights">
                  Menu
                </FooterLink>
                <FooterLink href="#weekend-specials">
                  Weekend Specials
                </FooterLink>
                <FooterLink href="#gallery">
                  Gallery
                </FooterLink>
                <FooterLink href="#contact">
                  Contact
                </FooterLink>
                <FooterLink href="/reservations">
                  Reservations
                </FooterLink>
                <FooterLink href="/catering">
                  Catering
                </FooterLink>
              </FooterSection>
            </Grid>
          </Grid>

          <Divider sx={{ borderColor: 'rgba(255, 255, 255, 0.1)', my: 4 }} />

          {/* Copyright */}
          <CopyrightSection>
            <Typography 
              variant="body2" 
              sx={{ 
                color: 'rgba(255, 255, 255, 0.6)',
                mb: 2
              }}
            >
              © 2024 Polo Restaurant. All rights reserved.
            </Typography>
            
            <Box sx={{ display: 'flex', justifyContent: 'center', gap: 3, flexWrap: 'wrap' }}>
              <FooterLink 
                href="/privacy" 
                sx={{ 
                  display: 'inline',
                  fontSize: '0.85rem',
                  mb: 0
                }}
              >
                Privacy Policy
              </FooterLink>
              <FooterLink 
                href="/terms" 
                sx={{ 
                  display: 'inline',
                  fontSize: '0.85rem',
                  mb: 0
                }}
              >
                Terms of Service
              </FooterLink>
              <FooterLink 
                href="/accessibility" 
                sx={{ 
                  display: 'inline',
                  fontSize: '0.85rem',
                  mb: 0
                }}
              >
                Accessibility
              </FooterLink>
            </Box>
            
            <Typography 
              variant="body2" 
              sx={{ 
                color: 'rgba(255, 255, 255, 0.4)',
                mt: 3,
                fontSize: '0.8rem'
              }}
            >
              Crafted with ❤️ in Switzerland
            </Typography>
          </CopyrightSection>
        </FooterContent>
      </Container>
    </StyledFooter>
  );
};

export default Footer;
