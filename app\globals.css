:root {
  --background: transparent
  --foreground: #000000;

  /* Swiss-Afghan Restaurant Color Palette */
  --saffron: #D4941A;
  --turquoise: #1E3A8A;
  --clay: #B87F16;
  --warm-cream: #F9ECCE;
  --swiss-red: #DC143C;
  --forest-green: #2D5D3F;
  --warm-grey: #F5F5F4;
  --dark-grey: #2D2D2D;

  /* Liquid Glass Effect Variables */
  --lg-bg-color: rgba(255, 255, 255, 0.25);
  --lg-highlight: rgba(255, 255, 255, 0.75);
  --lg-text: #000000;
  --lg-red: #fb4268;
  --lg-grey: #444739;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

a {
  color: inherit;
  text-decoration: none;
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}

/* Liquid Glass Effect Styles */
.glass-appbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: transparent;
  border-radius: 0 0 2rem 2rem;
  overflow: hidden;
  z-index: 1100;
  box-shadow: 0 6px 6px rgba(0, 0, 0, 0.2), 0 0 20px rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 2.2);
}
.glass-container {
  position: relative;
  display: flex;
  align-items: center;
  background: transparent;
  border-radius: 2rem;
  overflow: hidden;
  flex: 1 1 auto;
  box-shadow: 0 6px 6px rgba(0, 0, 0, 0.2), 0 0 20px rgba(0, 0, 0, 0.1);
  color: var(--lg-text);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 2.2);
}

.glass-container--rounded {
  border-radius: 3rem;
}

.glass-container--large {
  flex: 1 1 auto;
}

.glass-container--medium {
  flex: 1 1 auto;
}

.glass-container--small {
  flex: 0 1 auto;
}

.glass-filter,
.glass-overlay,
.glass-specular {
  position: absolute;
  inset: 0;
  border-radius: inherit;
}

.glass-filter {
  z-index: 0;
  backdrop-filter: blur(4px);
  filter: saturate(120%) brightness(1.15);
}

.glass-overlay {
  z-index: 1;
  background: var(--lg-bg-color);
}

.glass-specular {
  z-index: 2;
  box-shadow: inset 1px 1px 0 var(--lg-highlight),
    inset 0 0 5px var(--lg-highlight);
}

.glass-content {
  position: relative;
  z-index: 3;
  display: flex;
  flex: 1 1 auto;
  align-items: center;
  justify-content: space-around;
  padding: 12px 28px;
  gap: 1rem;
  flex-wrap: wrap;
}

.glass-content__link {
  margin-bottom: -1px;
  margin-top: 6px;
  transition: transform 0.2s ease-out;
}

.glass-content__link img {
  width: 78px;
}

.glass-content__link:hover {
  transform: scale(1.1);
}

.glass-content__link:active {
  transform: scale(0.95);
}

.glass-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  color: var(--lg-grey);
  transition: color 0.3s ease;
  cursor: pointer;
}

.glass-item svg {
  fill: var(--lg-grey);
  height: 50px;
  margin-bottom: 0.25rem;
  filter: drop-shadow(0 0 3px rgba(255 255 255 / 0.25));
  transition: transform 0.25s ease-out;
}

.glass-item svg:hover {
  transform: scale(1.1);
}

.glass-item svg:active {
  transform: scale(0.95);
}

.glass-item--active {
  background: rgba(0, 0, 0, 0.25);
  color: var(--lg-red);
  margin: -8px -40px;
  padding: 0.5rem 2.5rem;
  border-radius: 3rem;
}

.glass-item--active svg {
  fill: var(--lg-red);
}

/* Swiss-Afghan Restaurant Component Styles */
.restaurant-section {
  padding: 80px 0;
  position: relative;
}

.restaurant-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--warm-cream) 0%, rgba(249, 236, 206, 0.3) 100%);
  z-index: -1;
}

.afghan-pattern {
  background-image:
    radial-gradient(circle at 25% 25%, var(--saffron) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, var(--turquoise) 1px, transparent 1px);
  background-size: 40px 40px, 20px 20px;
  opacity: 0.1;
}

.modern-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(212, 148, 26, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-card:hover {
  transform: translateY(-8px);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 4px 16px rgba(212, 148, 26, 0.2);
}

.modern-button {
  background: linear-gradient(135deg, var(--saffron) 0%, var(--clay) 100%);
  color: white;
  border: none;
  border-radius: 50px;
  padding: 12px 32px;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.modern-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
}

.modern-button:hover::before {
  width: 300px;
  height: 300px;
}

.modern-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(212, 148, 26, 0.4);
}

.section-title {
  font-family: 'Playfair Display', serif;
  color: var(--dark-grey);
  text-align: center;
  margin-bottom: 60px;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, var(--saffron) 0%, var(--turquoise) 100%);
  border-radius: 2px;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .restaurant-section {
    padding: 60px 0;
  }

  .section-title {
    font-size: 2rem !important;
    margin-bottom: 40px;
  }

  .modern-card {
    margin-bottom: 20px;
  }

  .modern-button {
    padding: 10px 24px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .restaurant-section {
    padding: 40px 0;
  }

  .section-title {
    font-size: 1.75rem !important;
    margin-bottom: 30px;
  }

  .modern-card {
    border-radius: 15px;
  }

  .modern-button {
    padding: 8px 20px;
    font-size: 13px;
  }
}

/* Afghan Pattern Animations */
@keyframes afghanPattern {
  0% { background-position: 0% 0%; }
  50% { background-position: 100% 100%; }
  100% { background-position: 0% 0%; }
}

.afghan-pattern {
  animation: afghanPattern 20s ease-in-out infinite;
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--warm-grey);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--saffron) 0%, var(--turquoise) 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--clay) 0%, var(--swiss-red) 100%);
}
